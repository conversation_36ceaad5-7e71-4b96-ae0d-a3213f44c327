# 更新日志 (Changelog)

本文档记录了 HyperV_Manage 脚本的所有重要更新和改进。

## [v0.9.9] - 2024-12-19

### 🎉 新增功能
- **虚拟机导出功能**：完整的虚拟机导出解决方案
  - 支持批量选择和导出多个虚拟机
  - 智能处理运行中虚拟机的三种导出模式：
    - 包含内存状态（推荐）- 保存虚拟机当前运行状态
    - 生产检查点 - 使用 VSS 技术创建一致性快照
    - 崩溃一致性 - 不处理虚拟机状态
  - 交互式路径输入，自动创建导出目录
  - 详细的导出进度和结果反馈
  - 基于 Microsoft 官方 Export-VM cmdlet 实现

### 🔧 功能改进
- **错误提示显示优化**：解决错误信息一闪而过的问题
  - 所有错误提示现在至少显示1.5秒
  - 用户有足够时间阅读错误信息
  - 可通过 `DisplayDurationMs` 参数自定义显示时间
  - 智能计算已显示时间，确保总显示时间达到要求
  - 向后兼容，现有代码无需修改

- **空目录自动清理**：改进虚拟机创建体验
  - 虚拟机创建完成后自动清理空UUID目录
  - 虚拟机删除完成后自动清理空目录
  - 递归清理，按深度倒序删除
  - 安全检查，只删除完全为空的目录
  - 详细的清理进度和结果反馈

### 📚 文档更新
- 新增 `docs/导出功能说明.md` - 详细的导出功能使用指南
- 更新 `README.md` - 反映最新功能和改进
- 新增 `CHANGELOG.md` - 完整的版本更新记录

### 🛠️ 技术改进
- 改进 `Invoke-Graceful` 函数的错误处理机制
- 新增 `Export-VMCustom` 函数实现导出功能
- 新增 `Remove-EmptyDirectories` 函数实现目录清理
- 优化用户交互体验和界面反馈

---

## [v0.9.8] - 2024-12-18

### 🎉 新增功能
- **虚拟机导出功能基础框架**
- **改进的虚拟机管理界面**

### 🔧 功能改进
- 优化虚拟机创建流程
- 改进错误处理机制
- 增强用户界面响应性

---

## [v0.9.7] - 2024-12-17

### 🎉 新增功能
- **批量电源管理**：支持批量启动/关闭虚拟机
- **实时状态监控**：动态显示虚拟机状态变化
- **内存资源检查**：启动前检查系统内存资源

### 🔧 功能改进
- **交互式界面优化**：改进键盘导航体验
- **错误处理增强**：更友好的错误提示和建议
- **性能优化**：减少不必要的系统查询

### 📚 文档更新
- 完善功能说明文档
- 添加使用技巧和最佳实践

---

## [v0.9.6] - 2024-12-16

### 🎉 新增功能
- **虚拟机导入功能**：
  - 自动扫描虚拟机目录
  - UUID 冲突检测和处理
  - 兼容性问题自动修复
  - 网络和磁盘配置恢复

### 🔧 功能改进
- **网络管理完善**：
  - 支持外部、内部、专用交换机
  - 自动 NAT 配置
  - IP 转发设置
  - 网络状态实时显示

### 🐛 问题修复
- 修复虚拟机复制时的网络配置问题
- 解决磁盘挂载的路径识别问题

---

## [v0.9.5] - 2024-12-15

### 🎉 新增功能
- **磁盘管理模块**：
  - 创建、挂载、移除、删除虚拟磁盘
  - 智能磁盘选择和虚拟机匹配
  - 磁盘使用状态显示

### 🔧 功能改进
- **用户界面优化**：
  - 统一的交互式选择界面
  - 支持单选、多选、有序选择
  - 改进的键盘导航

### 📚 文档更新
- 添加详细的功能说明
- 完善安装和使用指南

---

## [v0.9.4] - 2024-12-14

### 🎉 新增功能
- **ISO 挂载管理**：
  - 多 ISO 文件挂载
  - 启动顺序设置
  - 自动路径扫描

### 🔧 功能改进
- **虚拟机复制优化**：
  - 完整配置复制
  - 磁盘文件自动重命名
  - 网络适配器配置保持

---

## [v0.9.3] - 2024-12-13

### 🎉 新增功能
- **配置管理系统**：
  - 持久化配置存储
  - 自动同步 Hyper-V 设置
  - 自定义存储路径

### 🔧 功能改进
- **虚拟机创建优化**：
  - 自动配置第二代虚拟机
  - 嵌套虚拟化支持
  - 安全启动配置

---

## [v0.9.2] - 2024-12-12

### 🎉 新增功能
- **网络管理基础功能**
- **虚拟机状态列表显示**

### 🔧 功能改进
- 改进错误处理机制
- 优化用户交互体验

---

## [v0.9.1] - 2024-12-11

### 🎉 新增功能
- **虚拟机复制功能**
- **虚拟机删除功能**

### 🔧 功能改进
- 统一的错误处理函数
- 改进的用户界面

---

## [v0.9.0] - 2024-12-10

### 🎉 初始版本
- **基础虚拟机管理**：创建虚拟机
- **自动提权机制**
- **基础交互式界面**
- **配置文件支持**

---

## 🔮 计划中的功能

### 下一版本 (v1.0.0)
- **虚拟机模板系统**：预定义虚拟机配置模板
- **批量操作增强**：更多批量管理功能
- **性能监控**：虚拟机性能指标显示
- **备份管理**：自动化备份和恢复
- **日志系统**：详细的操作日志记录

### 未来版本
- **远程管理**：支持远程 Hyper-V 主机管理
- **集群支持**：Hyper-V 集群管理功能
- **API 接口**：提供 REST API 接口
- **Web 界面**：基于 Web 的管理界面
- **自动化脚本**：虚拟机生命周期自动化

---

## 📝 贡献指南

如果您想为这个项目做出贡献，请：

1. Fork 这个仓库
2. 创建您的功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开一个 Pull Request

## 📞 支持

如果您遇到问题或有建议，请：
- 提交 Issue 到 GitHub 仓库
- 查看文档和常见问题解答
- 参考 `docs/` 目录下的详细说明

---

**注意**：版本号遵循 [语义化版本](https://semver.org/lang/zh-CN/) 规范。
