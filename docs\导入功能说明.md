# Hyper-V 管理脚本 - 虚拟机导入功能

## 功能概述

为功能1（虚拟机管理）添加了新的"导入"子功能，可以自动扫描指定路径下的虚拟机配置文件并进行批量导入。

## 功能特点

### 1. 自动路径检测
- 优先使用功能7中设置的默认虚拟机路径
- 如果未设置默认路径，提示用户手动输入路径
- 自动验证路径的有效性

### 2. 智能扫描
- 递归扫描指定目录下的所有 `.vmcx` 文件
- 以虚拟机文件夹名称作为虚拟机名称显示
- **基于UUID的重复检测**：从配置文件中读取虚拟机UUID，与现有虚拟机UUID比较
- 自动跳过UUID已存在的虚拟机，避免重复导入（无法通过改名绕过）

### 3. 交互式选择
- 显示所有可导入的虚拟机列表
- 支持多选，用户可以选择要导入的虚拟机
- 使用现有的通用交互函数，保持界面一致性

### 4. 官方导入方式
- 使用 Hyper-V 官方的 `Import-VM` 命令进行导入
- 保持虚拟机配置的完整性和兼容性

### 5. 磁盘文件检查
- 导入后自动检查虚拟机的磁盘配置
- 验证磁盘文件是否存在
- 显示磁盘文件状态（存在/不存在）

### 6. 错误处理
- 使用统一的 `Invoke-Graceful` 错误处理函数
- 避免直接抛出异常，提供友好的错误信息
- 单个虚拟机导入失败不影响其他虚拟机的导入

## 使用方法

1. 运行 Hyper-V 管理脚本
2. 选择功能1（管理）
3. 选择"导入"选项
4. 如果已设置默认路径，脚本会自动使用；否则需要手动输入路径
5. 脚本会扫描并显示可导入的虚拟机列表（包含UUID信息）
6. 使用上下箭头移动，空格键选择，回车确认
7. 脚本会逐个导入选中的虚拟机并显示结果

## UUID检测示例

```
获取现有虚拟机UUID列表...
当前系统中有 2 个虚拟机
  检查虚拟机：Hyper-V
    ✅ 虚拟机UUID未冲突，可以导入
  检查虚拟机：TestVM
    ❌ 虚拟机UUID已存在（现有虚拟机：TestVM-Copy），跳过

找到以下可导入的虚拟机：
> [ ] Hyper-V [UUID: 0AF503B3...] - D:\Virtual Machine\Hyper-V\Hyper-V\Virtual Machines\0AF503B3-608A-49E6-889E-58154220688D.vmcx
```

## 路径示例

假设虚拟机路径为 `D:\Virtual Machine\Hyper-V`，实际虚拟机配置文件路径为：
```
D:\Virtual Machine\Hyper-V\8\Virtual Machines\49DB5F7E-3C41-4945-A3D8-5C4A36584332.vmcx
```

在这个例子中：
- 虚拟机名称将显示为 "8"（文件夹名称）
- 脚本会自动找到并导入这个虚拟机

## 技术实现

### 主要函数
- `Import-VM`: 新增的导入功能主函数
- `Manage-VM`: 已更新，添加了导入选项

### 关键特性
- 使用 `Get-ChildItem -Recurse -Filter "*.vmcx"` 递归扫描
- 使用 `Split-Path` 获取文件夹名称作为虚拟机名称
- **UUID提取**：从.vmcx文件名或XML内容中提取虚拟机UUID
- **UUID比较**：与现有虚拟机UUID进行比较，确保唯一性
- 使用 `Interactive-Selection` 提供多选界面（显示UUID前8位）
- 使用 `Invoke-Graceful` 进行统一错误处理

### 配置集成
- 自动读取功能7中设置的虚拟机路径配置
- 自动读取磁盘路径配置用于验证磁盘文件

## 问题修复

### 版本更新 (完整修复版)

**修复的问题：**

1. **虚拟机名称显示错误**
   - 原问题：显示"Virtual Machines"而不是虚拟机文件夹名称
   - 修复方案：正确解析路径结构，获取虚拟机文件夹名称
   - 路径解析：`D:\Virtual Machine\Hyper-V\[VM_NAME]\Virtual Machines\[UUID].vmcx` → 提取 `[VM_NAME]`

2. **函数名冲突问题**
   - 原问题：自定义 `Import-VM` 函数与 PowerShell 内置命令冲突
   - 修复方案：重命名为 `Import-VMCustom` 避免冲突

3. **导入循环问题**
   - 原问题：选择后不停循环，无法正常退出
   - 修复方案：修复函数调用逻辑，确保正常的流程控制

4. **导入失败但显示成功问题** ⭐ **核心修复**
   - 原问题：虚拟机ID冲突导致导入失败，但显示成功信息
   - 修复方案：使用官方推荐的 `Compare-VM` + `Import-VM` 流程
   - 技术实现：
     ```powershell
     # 1. 先检查兼容性
     $report = Compare-VM -Path $configPath -Copy -GenerateNewId

     # 2. 自动修复常见问题（如网络适配器冲突）
     foreach ($issue in $report.Incompatibilities) {
         if ($issue.Source -is [VMNetworkAdapter]) {
             $issue.Source | Disconnect-VMNetworkAdapter
         }
     }

     # 3. 使用兼容性报告导入
     $vm = Import-VM -CompatibilityReport $report
     ```

5. **虚拟机ID冲突问题**
   - 原问题：导出的虚拟机与原虚拟机有相同ID，导致导入失败
   - 修复方案：使用 `-GenerateNewId` 参数生成新的唯一ID

6. **网络适配器冲突问题**
   - 原问题：导入时网络适配器连接到不存在的虚拟交换机
   - 修复方案：自动断开有问题的网络适配器连接

**技术细节：**
- 使用 `Split-Path` 正确解析虚拟机文件夹名称
- 避免在 `Invoke-Graceful` 内部使用 `Write-Host`
- 添加明确的成功/失败状态处理
- 实现完整的 Hyper-V 官方导入流程
- 自动处理常见的兼容性问题

## 新的导入流程

### 🔄 **完整导入流程**

1. **ID保持策略阶段**
   ```
   尝试保持原有虚拟机ID...
   ✓ 可以保持原有虚拟机ID
   尝试直接导入（保持原有ID）...
   虚拟机 'Hyper-V' 导入成功（保持原有ID）。
   ```
   - **优先策略**：尝试保持原有虚拟机ID
   - **兼容性检查**：使用 `Compare-VM` 检查是否有ID冲突
   - **直接导入**：如果无冲突，直接导入保持原有ID
   - **降级处理**：只有在真正有ID冲突时才生成新ID

2. **新ID生成阶段**（仅在ID冲突时）
   ```
   需要生成新的虚拟机ID以解决冲突
   使用新ID进行兼容性检查...
   发现兼容性问题：
     - Could not find Ethernet switch 'LAN'
     - Virtual Hard Disk file not found
   尝试自动修复兼容性问题...
     修复网络适配器连接问题...
       尝试连接到交换机 'Default Switch'
       ✓ 已连接到交换机 'Default Switch'
     修复磁盘文件问题...
       磁盘文件不存在：D:\Virtual Machine\Virtual Hard Disks\pve.vhdx
       移除不存在的磁盘驱动器
       ✓ 已移除不存在的磁盘驱动器
   ```
   - **条件触发**：只有在检测到真正的ID冲突时才执行
   - **新ID生成**：使用 `-GenerateNewId` 参数
   - **智能网络修复**：
     - 🥇 **优先策略**：首选 "Default Switch"
     - 🥈 **备选策略**：依次尝试其他可用交换机
     - 🚫 **降级处理**：所有交换机都失败时断开网络适配器
   - **智能磁盘修复**：自动移除不存在的磁盘文件
   - 处理其他常见兼容性问题
   - 重新验证兼容性

3. **实际导入阶段**
   ```
   虚拟机 '3' 导入成功。
   检查虚拟机磁盘配置...
   虚拟机 'VM-3-20250103' 的磁盘文件：
     ✓ D:\Virtual Machine\Hyper-V\VHDs\disk1.vhdx
   ```
   - 使用兼容性报告进行导入
   - 验证磁盘文件完整性
   - 显示最终的虚拟机名称

### 🎯 **解决的具体问题**

- ✅ **虚拟机ID策略**：智能ID处理策略
  - 🥇 **优先保持**：尽可能保持原有虚拟机ID
  - 🔄 **条件生成**：只有在真正冲突时才生成新ID
  - 🎯 **精确检测**：准确识别ID冲突情况
- ✅ **UUID重复检测**：基于UUID的智能重复检测
  - 🔍 **UUID提取**：从.vmcx文件中准确提取虚拟机UUID
  - 🛡️ **防绕过**：无法通过修改虚拟机名称绕过重复检测
  - 📊 **状态显示**：清晰显示检测过程和结果
- ✅ **删除功能增强**：完整的删除和清理流程
  - 📁 **路径一致性**：导入和删除使用相同的ID策略
  - 🗑️ **精确删除**：只删除实际导入的虚拟机文件
  - 🧹 **自动清理**：删除后自动清理虚拟机根目录下的空目录
  - 🔄 **递归清理**：从深层目录开始，逐级清理空目录
- ✅ **网络适配器冲突**：智能多级网络连接策略
  - 🥇 优先连接 "Default Switch"
  - 🥈 依次尝试其他可用交换机
  - 🚫 最后降级到断开连接
- ✅ **磁盘文件缺失**：自动移除不存在的磁盘驱动器
- ✅ **导入状态误报**：准确显示成功/失败状态
- ✅ **虚拟机名称显示**：正确显示文件夹名称和最终名称

## 注意事项

1. 需要管理员权限运行脚本
2. 确保虚拟机配置文件完整且未损坏
3. 导入前建议备份重要数据
4. 如果磁盘文件不存在，虚拟机仍会导入但无法正常启动
5. **虚拟机ID处理**：
   - 优先保持原有虚拟机ID，便于后续管理和删除
   - 只有在真正检测到ID冲突时才生成新ID
   - 生成新ID时虚拟机可能会被重命名（如 "3" → "VM-3-20250103"）
6. **网络适配器会智能连接**：
   - 优先连接到 "Default Switch"
   - 如果失败，会尝试其他可用交换机
   - 所有交换机都失败时会断开网络连接
7. **磁盘处理**：不存在的磁盘文件会被自动移除，虚拟机仍可正常导入
8. **后续配置**：如果需要特定的网络或存储配置，导入后可在 Hyper-V 管理器中调整

## 空目录清理功能

### 🧹 **自动清理机制**

删除虚拟机后，系统会自动清理虚拟机根目录下的空目录：

```powershell
# 清理指定路径下的空目录
function Remove-EmptyDirectories {
    param([string]$RootPath)

    # 递归获取所有目录，按深度倒序排列（先删除深层目录）
    $directories = Get-ChildItem -Path $RootPath -Directory -Recurse |
                   Sort-Object { $_.FullName.Split([System.IO.Path]::DirectorySeparatorChar).Count } -Descending

    foreach ($dir in $directories) {
        # 检查目录是否为空
        $items = Get-ChildItem -Path $dir.FullName -Force
        if ($items.Count -eq 0) {
            Remove-Item -Path $dir.FullName -Force
        }
    }
}
```

### 📋 **清理流程**

1. **删除虚拟机**：正常删除虚拟机和相关文件
2. **获取根路径**：从配置中获取虚拟机根目录路径
3. **递归扫描**：扫描根目录下的所有子目录
4. **深度排序**：按目录深度倒序排列（深层目录优先）
5. **空目录检测**：检查每个目录是否为空（无文件和子目录）
6. **安全删除**：只删除确认为空的目录

### 🎯 **清理示例**

```
开始清理空目录...
正在清理空目录：D:\Virtual Machine\Hyper-V
  ✓ 已删除空目录：D:\Virtual Machine\Hyper-V\TestVM\Virtual Machines
  ✓ 已删除空目录：D:\Virtual Machine\Hyper-V\TestVM
  ✓ 已删除空目录：D:\Virtual Machine\Hyper-V\OldVM\Snapshots
  ✓ 已删除空目录：D:\Virtual Machine\Hyper-V\OldVM
空目录清理完成，共删除 4 个空目录。

虚拟机删除和清理操作完成。
```

## 兼容性

- 兼容现有的脚本架构和交互模式
- 使用现有的错误处理机制
- 遵循现有的代码风格和命名规范
- 与功能7的路径配置完全集成
- 自动清理功能不影响现有虚拟机和文件
