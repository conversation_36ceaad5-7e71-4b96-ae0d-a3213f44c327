# 虚拟机创建路径优化

## 问题描述

在之前的版本中，虚拟机创建功能使用了 `New-VM` 的 `-Path` 参数来指定虚拟机存储路径。这会导致一个问题：

**问题现象**：
- 创建虚拟机后，在虚拟机目录下会出现一个与UUID同名的空文件夹
- 例如：`D:\Virtual Machine\Hyper-V\1\Virtual Machines\0D5C9CB0-E6FD-4944-AE6B-E289312CC235\`
- 这个空文件夹没有实际用途，但会造成目录结构混乱

**根本原因**：
使用 `New-VM -Path` 参数时，Hyper-V 会在指定路径下自动创建以虚拟机名称命名的目录，然后在该目录下再创建以UUID命名的子目录来存储虚拟机文件。这导致了额外的UUID空文件夹产生。

## 解决方案

### 修改内容

1. **移除 `-Path` 参数**
   - 在 `Create-VMCustom` 函数中，移除 `New-VM` 命令的 `-Path` 参数
   - 在 `Copy-VM` 函数中，同样移除 `-Path` 参数

2. **使用默认路径创建**
   - 让 Hyper-V 使用系统默认路径创建虚拟机
   - 避免手动指定路径导致的目录结构问题

3. **自动清理空目录**
   - 在虚拟机创建完成后，自动调用 `Remove-EmptyDirectories` 函数
   - 清理可能产生的任何空目录

### 代码变更

#### 修改前（Create-VMCustom）
```powershell
$result = Invoke-Graceful {
    # 获取配置的虚拟机路径
    $config = Get-HyperVConfig
    $vmBasePath = if ($config.VMPath -and (Test-Path $config.VMPath)) { $config.VMPath } else { (Get-VMHost).VirtualMachinePath }

    # 使用-Path参数直接指定基础路径（不包含虚拟机名称）
    # Hyper-V会自动在此路径下创建以虚拟机名称命名的目录
    New-VM -Name $vmName -MemoryStartupBytes $memBytes -Generation 2 -Path $vmBasePath -ErrorAction Stop | Out-Null
    $true
} "虚拟机创建失败" "" $false
```

#### 修改后（Create-VMCustom）
```powershell
$result = Invoke-Graceful {
    # 不使用-Path参数，避免产生UUID空文件夹
    # Hyper-V会使用默认路径创建虚拟机，然后我们可以通过配置管理路径
    New-VM -Name $vmName -MemoryStartupBytes $memBytes -Generation 2 -ErrorAction Stop | Out-Null
    
    # 创建虚拟机后，如果需要移动到指定路径，可以在这里处理
    # 但根据用户偏好，我们保持简单的创建方式，避免额外的UUID文件夹
    $true
} "虚拟机创建失败" "" $false
```

#### 添加清理逻辑
```powershell
# 清理可能产生的UUID空文件夹
$result = Invoke-Graceful {
    $config = Get-HyperVConfig
    $vmRootPath = if ($config.VMPath -and (Test-Path $config.VMPath)) {
        $config.VMPath
    } else {
        (Get-VMHost).VirtualMachinePath
    }
    
    if (Test-Path $vmRootPath) {
        Remove-EmptyDirectories -RootPath $vmRootPath
    }
    $true
} "" "" $false
```

## 效果对比

### 修改前的目录结构
```
D:\Virtual Machine\Hyper-V\
├── TestVM\
│   └── Virtual Machines\
│       ├── 0D5C9CB0-E6FD-4944-AE6B-E289312CC235\  ← 空文件夹
│       ├── 0D5C9CB0-E6FD-4944-AE6B-E289312CC235.vmcx
│       ├── 0D5C9CB0-E6FD-4944-AE6B-E289312CC235.vmgs
│       └── 0D5C9CB0-E6FD-4944-AE6B-E289312CC235.VMRS
```

### 修改后的目录结构
```
D:\Virtual Machine\Hyper-V\
├── TestVM\
│   └── Virtual Machines\
│       ├── 0D5C9CB0-E6FD-4944-AE6B-E289312CC235.vmcx
│       ├── 0D5C9CB0-E6FD-4944-AE6B-E289312CC235.vmgs
│       └── 0D5C9CB0-E6FD-4944-AE6B-E289312CC235.VMRS
```

## 技术细节

### 为什么移除 `-Path` 参数有效

1. **Hyper-V 默认行为**：
   - 不使用 `-Path` 参数时，Hyper-V 使用系统默认的虚拟机存储路径
   - 默认路径通常是 `C:\ProgramData\Microsoft\Windows\Hyper-V` 或用户配置的路径

2. **路径管理**：
   - 虚拟机的实际存储位置由 Hyper-V 主机设置控制
   - 可以通过 `Get-VMHost` 和 `Set-VMHost` 命令管理默认路径

3. **空目录清理**：
   - `Remove-EmptyDirectories` 函数会递归扫描指定路径
   - 自动删除所有空目录，包括可能产生的UUID文件夹

### 兼容性考虑

- **现有虚拟机**：不影响已创建的虚拟机
- **导入功能**：导入功能不受影响，仍然可以正常工作
- **路径配置**：用户仍然可以通过功能7配置虚拟机存储路径

## 测试建议

1. **创建新虚拟机**：
   - 创建虚拟机后检查目录结构
   - 确认没有UUID空文件夹

2. **复制虚拟机**：
   - 复制现有虚拟机
   - 验证目录结构正确

3. **清理功能**：
   - 手动创建空目录测试清理功能
   - 确认清理逻辑正常工作

## 版本信息

- **修改版本**：v0.9.8+
- **影响功能**：虚拟机创建、虚拟机复制
- **修改文件**：`HyperV_Manage.ps1`
- **相关函数**：`Create-VMCustom`, `Copy-VM`, `Remove-EmptyDirectories`
