# HyperV_Manage 安装配置指南

## 📋 目录

- [系统要求](#系统要求)
- [安装步骤](#安装步骤)
- [首次配置](#首次配置)
- [配置文件说明](#配置文件说明)
- [常见问题](#常见问题)
- [高级配置](#高级配置)

## 🖥️ 系统要求

### 硬件要求
- **处理器**：支持虚拟化技术的 64 位处理器
- **内存**：至少 4GB RAM（推荐 8GB 或更多）
- **存储**：至少 10GB 可用磁盘空间
- **网络**：网络适配器（用于外部交换机）

### 软件要求
- **操作系统**：
  - Windows 10 Pro/Enterprise/Education (版本 1903 或更高)
  - Windows 11 Pro/Enterprise/Education
  - Windows Server 2016/2019/2022
- **PowerShell**：PowerShell 5.1 或更高版本
- **Hyper-V**：已启用 Hyper-V 功能
- **权限**：管理员权限（脚本会自动提权）

### 功能要求检查
```powershell
# 检查 Hyper-V 功能是否启用
Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All

# 检查 PowerShell 版本
$PSVersionTable.PSVersion

# 检查当前用户权限
([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
```

## 📦 安装步骤

### 方法一：Git 克隆（推荐）
```powershell
# 1. 克隆仓库
git clone https://github.com/your-repo/HyperV_Manage.git
cd HyperV_Manage

# 2. 检查文件完整性
Get-ChildItem -Name

# 3. 运行脚本
.\HyperV_Manage.ps1
```

### 方法二：直接下载
1. 从 GitHub 下载 ZIP 文件
2. 解压到本地目录（如 `C:\Tools\HyperV_Manage`）
3. 打开 PowerShell 管理员窗口
4. 导航到解压目录
5. 运行脚本：`.\HyperV_Manage.ps1`

### 方法三：单文件部署
```powershell
# 下载单个脚本文件
Invoke-WebRequest -Uri "https://raw.githubusercontent.com/your-repo/HyperV_Manage/main/HyperV_Manage.ps1" -OutFile "HyperV_Manage.ps1"

# 运行脚本
.\HyperV_Manage.ps1
```

## ⚙️ 首次配置

### 1. 启用 Hyper-V 功能
如果 Hyper-V 未启用，请先启用：

```powershell
# 通过 PowerShell 启用（需要重启）
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All

# 或通过 DISM 启用
DISM /Online /Enable-Feature /All /FeatureName:Microsoft-Hyper-V
```

### 2. 设置 PowerShell 执行策略
```powershell
# 设置执行策略允许本地脚本运行
Set-ExecutionPolicy RemoteSigned -Scope CurrentUser

# 或者临时绕过执行策略
PowerShell -ExecutionPolicy Bypass -File ".\HyperV_Manage.ps1"
```

### 3. 首次运行
```powershell
# 运行脚本（会自动提权）
.\HyperV_Manage.ps1
```

首次运行时，脚本会：
- 检测管理员权限并自动提权
- 检查 Hyper-V 功能状态
- 创建默认配置文件
- 同步 Hyper-V 系统配置

### 4. 配置存储路径
运行脚本后，选择主菜单中的 "7. 目录" 来配置存储路径：

1. **虚拟机路径**：虚拟机配置文件存储位置
2. **虚拟磁盘路径**：VHD/VHDX 文件存储位置
3. **ISO 路径**：ISO 镜像文件目录

## 📄 配置文件说明

### HyperVConfig.json
脚本会在同目录下创建 `HyperVConfig.json` 配置文件：

```json
{
    "VHDPath": "D:\\Virtual Machine\\Hyper-V\\Virtual Hard Disks",
    "VMPath": "D:\\Virtual Machine\\Hyper-V\\Virtual Machines",
    "ISOPath": [
        "D:\\Virtual Machine\\ISO",
        "E:\\ISO"
    ]
}
```

### 配置项详解

#### VHDPath（虚拟磁盘路径）
- **用途**：存储 VHD/VHDX 虚拟磁盘文件
- **默认值**：Hyper-V 系统默认路径
- **建议**：选择有足够空间的磁盘
- **示例**：`"D:\\Virtual Machine\\Hyper-V\\Virtual Hard Disks"`

#### VMPath（虚拟机路径）
- **用途**：存储虚拟机配置文件
- **默认值**：Hyper-V 系统默认路径
- **建议**：与 VHDPath 在同一磁盘
- **示例**：`"D:\\Virtual Machine\\Hyper-V\\Virtual Machines"`

#### ISOPath（ISO 路径）
- **用途**：ISO 镜像文件搜索目录
- **默认值**：空数组
- **支持**：多个目录
- **示例**：`["D:\\ISO", "E:\\Images", "\\\\Server\\ISO"]`

### 配置文件管理
```powershell
# 查看当前配置
Get-Content "HyperVConfig.json" | ConvertFrom-Json

# 备份配置文件
Copy-Item "HyperVConfig.json" "HyperVConfig.backup.json"

# 重置配置（删除配置文件，重新运行脚本）
Remove-Item "HyperVConfig.json"
```

## ❓ 常见问题

### Q1: 脚本无法启动
**症状**：双击脚本文件无反应或出现错误

**解决方案**：
```powershell
# 检查执行策略
Get-ExecutionPolicy

# 设置执行策略
Set-ExecutionPolicy RemoteSigned -Scope CurrentUser

# 或使用绕过方式运行
PowerShell -ExecutionPolicy Bypass -File ".\HyperV_Manage.ps1"
```

### Q2: 提示 Hyper-V 功能未启用
**症状**：脚本提示 Hyper-V 功能未启用

**解决方案**：
```powershell
# 检查 Hyper-V 状态
Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All

# 启用 Hyper-V（需要重启）
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All
```

### Q3: 权限不足错误
**症状**：操作时提示权限不足

**解决方案**：
- 确保以管理员身份运行 PowerShell
- 脚本会自动尝试提权，按提示操作
- 检查 UAC 设置

### Q4: 配置文件丢失或损坏
**症状**：脚本提示配置文件错误

**解决方案**：
```powershell
# 删除损坏的配置文件
Remove-Item "HyperVConfig.json" -ErrorAction SilentlyContinue

# 重新运行脚本，会自动创建新配置
.\HyperV_Manage.ps1
```

### Q5: 虚拟机创建失败
**症状**：创建虚拟机时出现错误

**检查项目**：
- 磁盘空间是否充足
- 虚拟机名称是否重复
- 存储路径是否可写
- 内存分配是否合理

## 🔧 高级配置

### 自定义默认设置
编辑脚本文件，修改默认值：

```powershell
# 默认内存大小（行号约 520）
$memoryGB = 4

# 默认磁盘大小（行号约 530）
$diskSizeGB = 60

# 默认处理器数量（行号约 540）
$processorCount = 2
```

### 网络配置优化
```powershell
# 创建专用管理网络
New-VMSwitch -Name "Management" -SwitchType Internal

# 配置 NAT 网络
New-NetIPAddress -IPAddress ************* -PrefixLength 24 -InterfaceAlias "vEthernet (Management)"
New-NetNat -Name "ManagementNAT" -InternalIPInterfaceAddressPrefix *************/24
```

### 性能优化
```powershell
# 启用 Hyper-V 增强会话模式
Set-VMHost -EnableEnhancedSessionMode $true

# 配置动态内存
Set-VMMemory -VMName "YourVM" -DynamicMemoryEnabled $true -MinimumBytes 1GB -MaximumBytes 8GB
```

### 安全配置
```powershell
# 禁用安全启动（用于非 Windows 系统）
Set-VMFirmware -VMName "YourVM" -EnableSecureBoot Off

# 启用嵌套虚拟化
Set-VMProcessor -VMName "YourVM" -ExposeVirtualizationExtensions $true
```

## 📚 相关文档

- [功能特性总览](功能特性总览.md)
- [导出功能说明](导出功能说明.md)
- [更新日志](../CHANGELOG.md)
- [README](../README.md)

## 🆘 获取帮助

如果遇到问题：
1. 查看本文档的常见问题部分
2. 查看 [故障排除指南](故障排除指南.md) 获取详细解决方案
3. 检查 [CHANGELOG.md](../CHANGELOG.md) 了解已知问题
4. 在 GitHub 仓库提交 Issue
5. 查看 PowerShell 错误日志：`Get-EventLog -LogName Application -Source "PowerShell"`

## 📞 技术支持

- **文档**：查看 `docs/` 目录下的详细文档
- **示例**：参考脚本中的注释和示例
- **社区**：GitHub Discussions 和 Issues
- **更新**：定期检查新版本和功能更新

---

**注意**：配置更改后建议重启脚本以确保设置生效。某些 Hyper-V 功能更改可能需要重启系统。
