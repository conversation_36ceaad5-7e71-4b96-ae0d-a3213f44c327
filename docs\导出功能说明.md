# Hyper-V 管理脚本 - 虚拟机导出功能

## 功能概述

为功能1（虚拟机管理）添加了新的"导出"子功能，可以批量选择虚拟机并导出到指定路径。该功能基于 Hyper-V 官方的 `Export-VM` cmdlet 实现，确保导出的虚拟机完整性和兼容性。

**版本信息**：v0.9.9
**更新日期**：2024-12-19
**兼容性**：Windows 10/11 Pro/Enterprise, Windows Server 2016+

## 功能特点

### 1. 批量选择导出
- 支持多选虚拟机进行批量导出
- 实时显示虚拟机状态（运行中、已关闭、已保存）
- 使用直观的交互式界面进行选择

### 2. 智能状态处理
- 自动检测运行中的虚拟机
- 提供多种导出模式处理运行中的虚拟机：
  - **包含内存状态**（推荐）：保存虚拟机当前运行状态
  - **生产检查点**：使用 VSS 技术创建一致性快照
  - **崩溃一致性**：不处理虚拟机状态（可能导致数据不一致）

### 3. 路径管理
- 交互式输入导出目标文件夹路径
- 自动创建不存在的目录
- 路径权限和可写性验证
- 每个虚拟机导出到独立的子文件夹

### 4. 详细反馈
- 实时显示导出进度
- 成功/失败状态统计
- 详细的结果摘要表格
- 友好的错误提示和建议

## 使用方法

### 基本操作流程

1. **启动脚本**
   ```powershell
   .\HyperV_Manage.ps1
   ```

2. **进入虚拟机管理**
   - 选择主菜单中的 "1. 管理"

3. **选择导出功能**
   - 在管理子菜单中选择 "导出"

4. **选择虚拟机**
   - 使用上/下箭头移动光标
   - 使用空格键切换选择状态
   - 回车确认选择

5. **输入导出路径**
   - 输入目标文件夹路径（如：`D:\VM_Exports`）
   - 系统会自动创建不存在的目录

6. **选择导出模式**（如果有运行中的虚拟机）
   - 选择适合的导出模式
   - 推荐使用"包含内存状态"

7. **等待导出完成**
   - 查看实时进度反馈
   - 查看最终结果摘要

### 界面示例

#### 虚拟机选择界面
```
当前虚拟机状态：
══════════════════════════════════════════════════
  TestVM1 - Running
  TestVM2 - Off
  BackupVM - Saved

请选择要导出的虚拟机（使用上/下箭头移动，空格键切换选择状态，回车确认；按 ESC 返回；无选择则返回）：
-> [*] ● TestVM1 [Running]
   [ ] ○ TestVM2 [Off]
   [*] ◐ BackupVM [Saved]
```

#### 导出模式选择
```
检测到 1 个运行中的虚拟机：
  - TestVM1

请选择运行中虚拟机的导出方式：
1. 包含内存状态（推荐）- 保存虚拟机当前运行状态
2. 生产检查点 - 使用 VSS 技术创建一致性快照
3. 崩溃一致性 - 不处理虚拟机状态，可能导致数据不一致

请选择 (1-3) [默认: 1]: 1
已选择：包含内存状态
```

#### 导出进度显示
```
开始导出虚拟机...
══════════════════════════════════════════════════

正在导出虚拟机：TestVM1
状态：Running
✓ 虚拟机 'TestVM1' 导出成功

正在导出虚拟机：BackupVM
状态：Saved
✓ 虚拟机 'BackupVM' 导出成功
```

#### 结果摘要
```
导出操作完成
══════════════════════════════════════════════════
成功：2 个
失败：0 个
导出路径：D:\VM_Exports

详细结果：
Name      Status Path
----      ------ ----
TestVM1   成功   D:\VM_Exports\TestVM1
BackupVM  成功   D:\VM_Exports\BackupVM
```

## 导出文件结构

导出完成后，每个虚拟机会在目标路径下创建独立的文件夹，包含以下结构：

```
D:\VM_Exports\
├── TestVM1\
│   ├── Snapshots\          # 快照文件
│   ├── Virtual Hard Disks\ # 虚拟硬盘文件
│   └── Virtual Machines\   # 虚拟机配置文件
└── BackupVM\
    ├── Snapshots\
    ├── Virtual Hard Disks\
    └── Virtual Machines\
```

## 技术实现

### 核心功能
- **函数名称**：`Export-VMCustom`
- **调用位置**：`Manage-VM` 函数中的导出选项
- **基础命令**：使用 Hyper-V 官方的 `Export-VM` cmdlet

### 关键特性
- **多选界面**：使用 `Interactive-Selection` 函数提供多选支持
- **状态图标**：
  - `●` 运行中
  - `○` 已关闭  
  - `◐` 已保存
  - `?` 未知状态
- **错误处理**：使用 `Invoke-Graceful` 进行统一错误处理
- **路径验证**：创建测试文件验证路径可写性

### 导出参数
- **运行中虚拟机**：使用 `-CaptureLiveState` 参数
- **已停止虚拟机**：直接导出，无需特殊参数
- **导出路径**：使用 `-Path` 参数指定目标目录

## 注意事项

### 使用建议
1. **磁盘空间**：确保导出路径有足够的磁盘空间
2. **权限要求**：需要管理员权限运行脚本
3. **网络路径**：支持导出到网络路径（如 UNC 路径）
4. **运行状态**：建议在系统负载较低时进行导出操作

### 性能考虑
- 导出大型虚拟机可能需要较长时间
- 运行中虚拟机的导出会暂时影响性能
- 建议在非业务高峰期进行批量导出

### 故障排除
- **权限不足**：确保以管理员身份运行
- **磁盘空间不足**：检查目标路径可用空间
- **路径无效**：确认路径格式正确且可访问
- **虚拟机锁定**：确保虚拟机未被其他进程占用

## 与导入功能的配合

导出的虚拟机可以使用脚本中的"导入"功能重新导入：

1. **导出虚拟机**：使用导出功能创建备份
2. **移动文件**：将导出的文件夹复制到目标位置
3. **导入虚拟机**：使用导入功能恢复虚拟机

这样可以实现虚拟机的完整备份和迁移流程。

## 版本信息

- **添加版本**：v0.9.8
- **基于官方文档**：Microsoft Learn - Export-VM cmdlet
- **兼容性**：Windows 10/11 Pro/Enterprise, Windows Server 2016+
- **依赖功能**：Interactive-Selection, Invoke-Graceful

## 最新改进 (v0.9.9)

### 错误提示显示时间优化

**问题描述**：之前的错误提示显示时间过短，用户来不及阅读就消失了。

**解决方案**：改进了 `Invoke-Graceful` 函数，增加了错误提示的最小显示时间：

- **默认显示时间**：1.5秒（1500毫秒）
- **可自定义时间**：通过 `DisplayDurationMs` 参数调整
- **智能计算**：自动计算已显示时间，确保总显示时间达到要求

**技术实现**：
```powershell
function Invoke-Graceful {
    param(
        [Parameter(Mandatory=$true)]
        [ScriptBlock]$Script,
        [string]$ErrorMessage = "操作失败",
        [string]$WarnMessage = "",
        [object]$Default = $null,
        [int]$DisplayDurationMs = 1500  # 错误提示显示时间（毫秒）
    )
    try {
        return & $Script
    } catch {
        $startTime = Get-Date

        # 显示错误信息
        if ($WarnMessage) { Write-Warning $WarnMessage }
        Write-Warning "$ErrorMessage：$($_.Exception.Message)"

        # 确保显示足够长的时间
        $elapsedMs = ((Get-Date) - $startTime).TotalMilliseconds
        $remainingMs = $DisplayDurationMs - $elapsedMs
        if ($remainingMs -gt 0) {
            Start-Sleep -Milliseconds $remainingMs
        }

        return $Default
    }
}
```

**用户体验改进**：
- 所有错误提示现在至少显示1.5秒
- 用户有足够时间阅读错误信息
- 不再出现一闪而过的提示
- 保持了脚本的响应性和流畅性
