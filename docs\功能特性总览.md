# HyperV_Manage 功能特性总览

## 📋 目录

- [核心功能模块](#核心功能模块)
- [用户体验特性](#用户体验特性)
- [技术特性](#技术特性)
- [安全特性](#安全特性)
- [性能特性](#性能特性)

## 🚀 核心功能模块

### 1. 虚拟机管理 (Manage-VM)
- **创建虚拟机**
  - 自定义名称、内存、处理器、磁盘大小
  - 自动配置第二代虚拟机
  - 关闭安全启动，启用嵌套虚拟化
  - 智能网络配置和 ISO 挂载
  - 创建完成后自动清理空UUID目录

- **复制虚拟机**
  - 完整复制虚拟机配置和磁盘
  - 保持原有网络和启动设置
  - 自动处理磁盘文件命名冲突
  - 智能网络适配器配置

- **删除虚拟机**
  - 安全删除虚拟机和关联磁盘
  - 删除完成后自动清理空目录
  - 支持批量删除操作
  - 运行状态检查和确认

- **导入虚拟机**
  - 自动扫描虚拟机目录
  - UUID 冲突检测和处理
  - 兼容性问题自动修复
  - 网络和磁盘配置恢复

- **导出虚拟机** ⭐ 新功能
  - 支持批量选择和导出虚拟机
  - 智能处理运行中的虚拟机状态
  - 三种导出模式：内存状态、生产检查点、崩溃一致性
  - 自动创建导出目录和文件夹结构
  - 详细的导出进度和结果反馈

### 2. 网络管理 (Manage-Network)
- **虚拟交换机管理**
  - 外部交换机：绑定物理网卡，提供外网访问
  - 内部交换机：主机与虚拟机通信，支持 NAT 配置
  - 专用交换机：仅虚拟机间通信

- **高级网络功能**
  - 自动 IP 配置和 NAT 设置
  - IP 转发启用
  - 网络状态实时显示
  - 关联资源自动清理

### 3. 磁盘管理 (Manage-Disks)
- **磁盘操作**
  - 创建：支持 VHD/VHDX 格式，动态扩展
  - 挂载：智能磁盘选择和虚拟机匹配
  - 移除：安全移除磁盘挂载
  - 删除：批量删除磁盘文件

- **智能特性**
  - 磁盘使用状态显示
  - 运行状态安全检查
  - 自动去重和排序
  - 磁盘大小信息显示

### 4. 电源管理 (Manage-VMPowerState)
- **批量操作**
  - 可视化虚拟机状态
  - 批量启动/关闭选择
  - 实时状态更新
  - 资源检查和警告

- **安全特性**
  - 内存资源预检查
  - 启动超时检测
  - 详细错误诊断
  - 操作状态反馈

### 5. ISO 挂载管理 (Mount-ISO)
- **多 ISO 支持**
  - 多 ISO 文件挂载
  - 启动顺序设置
  - 自动路径扫描
  - 挂载状态检查

### 6. 配置管理 (Set-BaseDirs)
- **路径配置**
  - 虚拟机存储路径
  - 虚拟磁盘存储路径
  - ISO 镜像目录（支持多个）
  - 配置持久化存储

### 7. 其他功能 (Manage-Other)
- **嵌套虚拟化管理**
- **资源计量管理**
- **系统优化设置**

## 🎨 用户体验特性

### 交互式界面
- **键盘导航**：上/下箭头移动，空格切换，回车确认，ESC返回
- **多选支持**：支持单选、多选、有序选择
- **实时反馈**：操作状态实时显示
- **颜色编码**：不同状态使用不同颜色标识

### 智能提示
- **错误提示优化** ⭐ 新改进
  - 所有错误提示至少显示1.5秒
  - 用户有足够时间阅读错误信息
  - 可自定义显示时间
  - 智能计算显示时长

- **操作指导**
  - 详细的操作说明
  - 参数提示和默认值
  - 操作确认和警告

### 自动化特性
- **自动提权**：检测管理员权限，自动提升权限运行
- **自动清理** ⭐ 新改进：虚拟机创建/删除后自动清理空目录
- **自动配置**：智能的默认配置和自动优化

## 🔧 技术特性

### 错误处理
- **统一错误处理**：`Invoke-Graceful` 函数统一处理所有错误
- **优雅降级**：错误时提供默认值和备选方案
- **详细诊断**：提供具体的错误信息和解决建议

### 配置管理
- **JSON 配置文件**：使用 `HyperVConfig.json` 存储配置
- **自动同步**：与 Hyper-V 系统配置自动同步
- **配置验证**：启动时验证配置有效性

### 模块化设计
- **功能模块化**：每个功能独立模块，便于维护
- **通用函数库**：可重用的通用函数
- **插件式架构**：易于扩展新功能

## 🔒 安全特性

### 权限管理
- **管理员权限检查**：确保具有必要的系统权限
- **安全操作确认**：危险操作需要用户确认
- **状态验证**：操作前检查虚拟机状态

### 数据保护
- **备份建议**：重要操作前提醒备份
- **完整性检查**：导入/导出时验证文件完整性
- **冲突检测**：避免重名和UUID冲突

### 操作安全
- **运行状态检查**：操作前检查虚拟机运行状态
- **资源检查**：确保系统资源充足
- **回滚机制**：失败时尽可能恢复原状态

## ⚡ 性能特性

### 优化策略
- **批量操作**：支持批量处理减少重复操作
- **缓存机制**：缓存常用信息减少系统查询
- **异步处理**：长时间操作提供进度反馈

### 资源管理
- **内存检查**：启动虚拟机前检查可用内存
- **磁盘空间检查**：创建虚拟机前检查磁盘空间
- **网络资源管理**：智能分配网络资源

### 响应性
- **实时更新**：状态信息实时刷新
- **快速响应**：界面操作即时响应
- **超时处理**：长时间操作设置合理超时

## 📊 统计信息

### 代码规模
- **总行数**：约 3,000+ 行 PowerShell 代码
- **函数数量**：30+ 个功能函数
- **模块数量**：8 个主要功能模块

### 功能覆盖
- **虚拟机生命周期**：创建、复制、删除、导入、导出
- **资源管理**：网络、磁盘、ISO、电源
- **系统管理**：配置、监控、优化

### 兼容性
- **操作系统**：Windows 10/11 Pro/Enterprise, Windows Server 2016+
- **PowerShell**：5.1+ 版本
- **Hyper-V**：所有现代版本

## 🎯 使用场景

### 开发环境
- 快速创建开发虚拟机
- 环境复制和分发
- 测试环境管理

### 生产环境
- 虚拟机备份和恢复
- 批量虚拟机管理
- 系统维护和优化

### 教育培训
- 实验环境快速部署
- 学生环境标准化
- 课程资源管理

### 个人使用
- 多系统测试
- 软件隔离运行
- 系统备份和恢复

---

**注意**：本文档反映的是 v0.9.9 版本的功能特性。随着版本更新，功能可能会有所变化。请参考最新的 README.md 和 CHANGELOG.md 获取最新信息。
