# HyperV_Manage 故障排除指南

## 📋 目录

- [常见启动问题](#常见启动问题)
- [虚拟机操作问题](#虚拟机操作问题)
- [网络配置问题](#网络配置问题)
- [磁盘管理问题](#磁盘管理问题)
- [权限和安全问题](#权限和安全问题)
- [性能问题](#性能问题)
- [错误代码参考](#错误代码参考)

## 🚀 常见启动问题

### 问题1：脚本无法启动
**症状**：
- 双击脚本文件无反应
- 出现"无法加载文件"错误
- PowerShell 窗口闪退

**原因分析**：
- PowerShell 执行策略限制
- 文件路径包含特殊字符
- 权限不足

**解决方案**：
```powershell
# 方案1：设置执行策略
Set-ExecutionPolicy RemoteSigned -Scope CurrentUser

# 方案2：临时绕过执行策略
PowerShell -ExecutionPolicy Bypass -File ".\HyperV_Manage.ps1"

# 方案3：检查文件路径
# 确保路径不包含中文或特殊字符
```

### 问题2：Hyper-V 功能检测失败
**症状**：
- 提示"Hyper-V 功能未启用"
- 无法检测到 Hyper-V 服务

**诊断命令**：
```powershell
# 检查 Hyper-V 功能状态
Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All

# 检查 Hyper-V 服务
Get-Service -Name vmms

# 检查虚拟化支持
systeminfo | findstr /i hyper
```

**解决方案**：
```powershell
# 启用 Hyper-V 功能（需要重启）
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All

# 或使用 DISM
DISM /Online /Enable-Feature /All /FeatureName:Microsoft-Hyper-V

# 检查 BIOS 虚拟化设置
# 确保在 BIOS 中启用了 Intel VT-x 或 AMD-V
```

### 问题3：自动提权失败
**症状**：
- UAC 提示后脚本仍然报权限错误
- 无法执行管理员操作

**解决方案**：
```powershell
# 手动以管理员身份运行 PowerShell
# 右键点击 PowerShell -> "以管理员身份运行"

# 检查当前权限
([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

# 如果返回 False，需要重新以管理员身份运行
```

## 🖥️ 虚拟机操作问题

### 问题4：虚拟机创建失败
**症状**：
- 创建过程中断
- 提示磁盘空间不足
- 虚拟机创建后无法启动

**诊断步骤**：
```powershell
# 检查磁盘空间
Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID, FreeSpace, Size

# 检查内存使用
Get-WmiObject -Class Win32_OperatingSystem | Select-Object TotalVisibleMemorySize, FreePhysicalMemory

# 检查 Hyper-V 配置
Get-VMHost
```

**解决方案**：
1. **磁盘空间不足**：
   ```powershell
   # 清理磁盘空间或更改存储路径
   # 通过主菜单 "7. 目录" 修改存储路径
   ```

2. **内存不足**：
   ```powershell
   # 减少虚拟机内存分配
   # 或关闭其他虚拟机释放内存
   ```

3. **虚拟机名称冲突**：
   ```powershell
   # 检查现有虚拟机
   Get-VM | Select-Object Name, State
   
   # 使用不同的虚拟机名称
   ```

### 问题5：虚拟机导入失败
**症状**：
- 导入过程中断
- 提示配置文件损坏
- UUID 冲突错误

**解决方案**：
```powershell
# 检查导入文件完整性
Get-ChildItem -Path "导入路径" -Recurse

# 手动导入（如果自动导入失败）
Import-VM -Path "虚拟机配置文件路径"

# 生成新 UUID（如果冲突）
Import-VM -Path "配置文件路径" -GenerateNewId
```

### 问题6：虚拟机导出失败
**症状**：
- 导出过程中断
- 运行中虚拟机导出失败
- 目标路径权限错误

**解决方案**：
1. **运行中虚拟机**：
   ```powershell
   # 选择合适的导出模式
   # 1. 包含内存状态（推荐）
   # 2. 生产检查点
   # 3. 崩溃一致性
   ```

2. **权限问题**：
   ```powershell
   # 检查目标路径权限
   Test-Path "导出路径" -PathType Container
   
   # 创建测试文件验证写权限
   New-Item -Path "导出路径\test.txt" -ItemType File
   ```

## 🌐 网络配置问题

### 问题7：虚拟交换机创建失败
**症状**：
- 无法创建外部交换机
- 网络适配器绑定失败
- 虚拟机无网络连接

**诊断命令**：
```powershell
# 检查网络适配器
Get-NetAdapter | Where-Object {$_.Status -eq "Up"}

# 检查现有虚拟交换机
Get-VMSwitch

# 检查网络配置
Get-NetIPConfiguration
```

**解决方案**：
1. **外部交换机创建失败**：
   ```powershell
   # 确保网络适配器未被占用
   # 检查是否有其他虚拟交换机使用该适配器
   
   # 手动创建外部交换机
   New-VMSwitch -Name "External" -NetAdapterName "以太网" -AllowManagementOS $true
   ```

2. **内部交换机 NAT 配置失败**：
   ```powershell
   # 检查现有 NAT 配置
   Get-NetNat
   
   # 删除冲突的 NAT 配置
   Remove-NetNat -Name "冲突的NAT名称"
   
   # 重新配置
   New-NetIPAddress -IPAddress ************* -PrefixLength 24 -InterfaceAlias "vEthernet (Internal)"
   New-NetNat -Name "InternalNAT" -InternalIPInterfaceAddressPrefix *************/24
   ```

### 问题8：虚拟机网络连接问题
**症状**：
- 虚拟机无法访问网络
- 无法获取 IP 地址
- 网络速度慢

**解决方案**：
```powershell
# 检查虚拟机网络适配器
Get-VMNetworkAdapter -VMName "虚拟机名称"

# 重新连接到交换机
Connect-VMNetworkAdapter -VMName "虚拟机名称" -SwitchName "交换机名称"

# 启用 MAC 地址欺骗（如果需要）
Set-VMNetworkAdapter -VMName "虚拟机名称" -MacAddressSpoofing On
```

## 💾 磁盘管理问题

### 问题9：虚拟磁盘创建失败
**症状**：
- 磁盘创建过程中断
- 提示磁盘格式错误
- 磁盘大小设置失败

**解决方案**：
```powershell
# 检查磁盘空间
Get-PSDrive

# 手动创建虚拟磁盘
New-VHD -Path "磁盘路径.vhdx" -SizeBytes 60GB -Dynamic

# 检查磁盘格式支持
# VHDX 格式支持更大容量和更好性能
```

### 问题10：磁盘挂载失败
**症状**：
- 无法挂载磁盘到虚拟机
- 磁盘路径无效
- 控制器位置冲突

**解决方案**：
```powershell
# 检查虚拟机磁盘控制器
Get-VMScsiController -VMName "虚拟机名称"
Get-VMIdeController -VMName "虚拟机名称"

# 手动挂载磁盘
Add-VMHardDiskDrive -VMName "虚拟机名称" -Path "磁盘路径" -ControllerType SCSI

# 检查磁盘文件是否存在
Test-Path "磁盘路径"
```

## 🔒 权限和安全问题

### 问题11：权限不足错误
**症状**：
- 操作被拒绝
- 无法访问系统资源
- UAC 提示频繁出现

**解决方案**：
```powershell
# 检查当前用户权限
whoami /priv

# 检查 Hyper-V 管理员组成员
Get-LocalGroupMember -Group "Hyper-V Administrators"

# 将用户添加到 Hyper-V 管理员组
Add-LocalGroupMember -Group "Hyper-V Administrators" -Member "用户名"
```

### 问题12：文件访问被拒绝
**症状**：
- 无法读取/写入配置文件
- 虚拟机文件访问失败
- 日志文件创建失败

**解决方案**：
```powershell
# 检查文件权限
Get-Acl "文件路径"

# 修改文件权限
$acl = Get-Acl "文件路径"
$accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("用户名","FullControl","Allow")
$acl.SetAccessRule($accessRule)
Set-Acl "文件路径" $acl
```

## ⚡ 性能问题

### 问题13：脚本运行缓慢
**症状**：
- 界面响应慢
- 虚拟机列表加载时间长
- 操作执行缓慢

**优化方案**：
```powershell
# 检查系统资源使用
Get-Process | Sort-Object CPU -Descending | Select-Object -First 10

# 优化 PowerShell 性能
$ProgressPreference = 'SilentlyContinue'

# 减少不必要的查询
# 关闭不需要的虚拟机以减少系统负载
```

### 问题14：虚拟机性能问题
**症状**：
- 虚拟机启动慢
- 运行性能差
- 内存使用率高

**优化方案**：
```powershell
# 启用动态内存
Set-VMMemory -VMName "虚拟机名称" -DynamicMemoryEnabled $true

# 调整处理器配置
Set-VMProcessor -VMName "虚拟机名称" -Count 2

# 启用增强会话模式
Set-VMHost -EnableEnhancedSessionMode $true
```

## 📊 错误代码参考

### 常见错误代码
- **0x80070005**：访问被拒绝，权限不足
- **0x80070020**：文件被占用，无法访问
- **0x8007000E**：内存不足
- **0x80070070**：磁盘空间不足
- **0x80041010**：WMI 服务问题

### 错误处理
```powershell
# 查看详细错误信息
$Error[0] | Format-List * -Force

# 清除错误记录
$Error.Clear()

# 启用详细日志
$VerbosePreference = 'Continue'
```

## 🆘 获取更多帮助

### 日志文件位置
- **Windows 事件日志**：事件查看器 > Windows 日志 > 应用程序
- **Hyper-V 日志**：事件查看器 > 应用程序和服务日志 > Microsoft > Windows > Hyper-V-*
- **PowerShell 日志**：`$PROFILE` 目录下的日志文件

### 诊断工具
```powershell
# 系统信息
systeminfo

# Hyper-V 配置
Get-VMHost | Format-List *

# 网络配置
ipconfig /all
Get-NetAdapter
Get-VMSwitch

# 性能计数器
Get-Counter "\Hyper-V Hypervisor\Virtual Processors"
```

### 联系支持
- **GitHub Issues**：报告 bug 和功能请求
- **文档**：查看 `docs/` 目录下的其他文档
- **社区**：参与 GitHub Discussions

---

**提示**：遇到问题时，请先查看错误信息的详细内容，这通常包含解决问题的关键信息。如果问题持续存在，请收集相关日志信息并在 GitHub 上提交 Issue。
