# HyperV_Manage - Hyper-V 虚拟机管理工具

一个功能强大的 PowerShell 脚本，用于简化 Windows Hyper-V 虚拟机的管理操作。提供直观的交互式界面，支持虚拟机创建、复制、删除、导入导出、网络配置、磁盘管理等全方位功能。

## 🚀 主要特性

### 核心功能
- **虚拟机管理**：创建、复制、删除、导入、导出虚拟机
- **电源管理**：批量启动/关闭虚拟机，实时状态监控
- **网络管理**：创建/删除虚拟交换机，支持外部、内部、专用网络
- **磁盘管理**：创建、挂载、移除、删除虚拟磁盘
- **ISO 挂载**：支持多 ISO 文件挂载和启动顺序设置
- **配置管理**：持久化存储路径配置，自动同步设置

### 用户体验
- **自动提权**：检测管理员权限，自动提升权限运行
- **交互式界面**：键盘导航，支持单选、多选、有序选择
- **智能错误处理**：优雅的错误处理，错误提示至少显示1.5秒
- **配置持久化**：自动保存和同步 Hyper-V 配置
- **实时状态**：动态显示虚拟机状态和资源信息
- **自动清理**：虚拟机创建后自动清理空UUID目录

## 📋 系统要求

- **操作系统**：Windows 10/11 Pro/Enterprise 或 Windows Server 2016+
- **Hyper-V**：已启用 Hyper-V 功能
- **PowerShell**：PowerShell 5.1 或更高版本
- **权限**：管理员权限（脚本会自动提权）

## 🛠️ 安装与使用

### 快速开始

1. **下载脚本**
   ```powershell
   # 下载到本地目录
   git clone https://github.com/your-repo/HyperV_Manage.git
   cd HyperV_Manage
   ```

2. **运行脚本**
   ```powershell
   # 直接运行（会自动提权）
   .\HyperV_Manage.ps1
   ```

3. **首次配置**
   - 脚本会自动检测并同步 Hyper-V 配置
   - 可通过主菜单 "7. 目录" 自定义存储路径

### 主菜单功能

```
【主菜单】
══════════════════════════════════════════════════
1. 管理    - 虚拟机创建、复制、删除、导入、导出
2. 网络    - 虚拟交换机管理
3. 磁盘    - 虚拟磁盘管理
4. 挂载    - ISO 镜像挂载
5. 列表    - 虚拟机状态查看
6. 电源    - 批量电源管理
7. 目录    - 路径配置管理
8. 其他    - 嵌套虚拟化、资源计量
9. 控制台  - 打开 Hyper-V 管理控制台
0. 退出    - 退出脚本
══════════════════════════════════════════════════
```

## 📖 详细功能说明

### 1. 虚拟机管理

#### 创建虚拟机
- 自定义虚拟机名称、内存、处理器、磁盘大小
- 自动配置第二代虚拟机
- 关闭安全启动，启用嵌套虚拟化
- 智能网络配置和 ISO 挂载
- 创建完成后自动清理空UUID目录

#### 复制虚拟机
- 完整复制虚拟机配置和磁盘
- 保持原有网络和启动设置
- 自动处理磁盘文件命名冲突
- 智能网络适配器配置

#### 删除虚拟机
- 安全删除虚拟机和关联磁盘
- 删除完成后自动清理空目录
- 支持批量删除操作
- 运行状态检查和确认

#### 导入虚拟机
- 自动扫描虚拟机目录
- UUID 冲突检测和处理
- 兼容性问题自动修复
- 网络和磁盘配置恢复

#### 导出虚拟机
- 支持批量选择和导出虚拟机
- 智能处理运行中的虚拟机状态
- 多种导出模式：内存状态、生产检查点、崩溃一致性
- 自动创建导出目录和文件夹结构
- 详细的导出进度和结果反馈

### 2. 网络管理

#### 虚拟交换机类型
- **外部交换机**：绑定物理网卡，提供外网访问
- **内部交换机**：主机与虚拟机通信，支持 NAT 配置
- **专用交换机**：仅虚拟机间通信

#### 高级网络功能
- 自动 IP 配置和 NAT 设置
- IP 转发启用
- 网络状态实时显示
- 关联资源自动清理

### 3. 磁盘管理

#### 磁盘操作
- **创建**：支持 VHD/VHDX 格式，动态扩展
- **挂载**：智能磁盘选择和虚拟机匹配
- **移除**：安全移除磁盘挂载
- **删除**：批量删除磁盘文件

#### 智能特性
- 磁盘使用状态显示
- 运行状态安全检查
- 自动去重和排序
- 磁盘大小信息显示

### 4. 电源管理

#### 批量操作
- 可视化虚拟机状态
- 批量启动/关闭选择
- 实时状态更新
- 资源检查和警告

#### 安全特性
- 内存资源预检查
- 启动超时检测
- 详细错误诊断
- 操作状态反馈

## ⚙️ 配置文件

脚本使用 `HyperVConfig.json` 存储配置：

```json
{
    "VHDPath": "D:\\Virtual Machine\\Hyper-V\\Virtual Hard Disks",
    "VMPath": "D:\\Virtual Machine\\Hyper-V\\Virtual Machines", 
    "ISOPath": [
        "D:\\Virtual Machine\\ISO",
        "E:\\ISO"
    ]
}
```

### 配置项说明
- **VHDPath**：虚拟磁盘存储路径
- **VMPath**：虚拟机文件存储路径  
- **ISOPath**：ISO 镜像文件目录（支持多个）

## 🎯 使用技巧

### 键盘操作
- **上/下箭头**：移动选择
- **空格键**：切换选择状态
- **回车键**：确认操作
- **ESC 键**：返回上级菜单

### 最佳实践
1. **定期备份**：重要虚拟机定期导出备份
2. **资源监控**：注意系统内存和磁盘空间
3. **网络规划**：合理规划虚拟网络拓扑
4. **路径管理**：统一管理虚拟机存储路径

## 🔧 故障排除

### 常见问题

**Q: 脚本无法启动？**
A: 检查 PowerShell 执行策略：`Set-ExecutionPolicy RemoteSigned`

**Q: 虚拟机创建失败？**
A: 确认 Hyper-V 功能已启用，检查磁盘空间和权限

**Q: 网络配置失败？**
A: 检查网络适配器状态，确认防火墙设置

**Q: 导入虚拟机失败？**
A: 检查虚拟机文件完整性，确认路径配置正确

### 日志和调试
- 脚本提供详细的错误信息和操作提示
- 使用 `Invoke-Graceful` 函数进行统一错误处理
- 错误提示至少显示1.5秒，确保用户有足够时间阅读
- 支持操作状态实时反馈
- 自动清理功能减少文件系统混乱

## 📝 版本信息

**当前版本**：0.9.9

### 最新更新 (v0.9.9)
- **新增虚拟机导出功能**：支持批量导出，多种导出模式
- **错误提示显示优化**：所有错误提示至少显示1.5秒，用户体验更友好
- **空目录自动清理**：虚拟机创建后自动清理空UUID目录

### 更新日志
- v0.9.8: 添加虚拟机导出功能，支持批量导出和多种导出模式
- v0.9.7: 优化交互式界面体验，增强错误处理机制
- v0.9.6: 改进虚拟机导入功能，完善网络管理功能
- v0.9.5: 添加批量电源管理，优化用户界面

## 📚 详细文档

### 核心文档
- **[功能特性总览](docs/功能特性总览.md)** - 完整的功能特性介绍
- **[安装配置指南](docs/安装配置指南.md)** - 详细的安装和配置说明
- **[故障排除指南](docs/故障排除指南.md)** - 常见问题和解决方案

### 专项文档
- **[导出功能说明](docs/导出功能说明.md)** - 虚拟机导出功能详细指南
- **[更新日志](CHANGELOG.md)** - 完整的版本更新记录

### 快速链接
- [系统要求](#系统要求)
- [安装步骤](docs/安装配置指南.md#安装步骤)
- [首次配置](docs/安装配置指南.md#首次配置)
- [常见问题](docs/故障排除指南.md#常见启动问题)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

### 贡献指南
1. Fork 这个仓库
2. 创建您的功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开一个 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户。

## 📞 支持

如果您需要帮助：
- 查看 [故障排除指南](docs/故障排除指南.md)
- 提交 [GitHub Issue](https://github.com/your-repo/HyperV_Manage/issues)
- 参与 [GitHub Discussions](https://github.com/your-repo/HyperV_Manage/discussions)

---

**注意**：使用本脚本前请确保已备份重要数据，虚拟机操作具有一定风险性。
